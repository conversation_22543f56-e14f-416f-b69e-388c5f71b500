#!/usr/bin/env python3
"""
Configuration file for MCAT Question Generator
"""

import os
from pathlib import Path

# Load OPENAI_API_KEY from a local .env file without exposing it.
# Supports standard `KEY=VALUE` and the current `KEY- VALUE` format.
def _load_api_key_from_dotenv():
    if os.getenv('OPENAI_API_KEY'):
        return  # already set by environment
    env_path = Path(__file__).parent / '.env'
    if not env_path.exists():
        return
    try:
        with env_path.open('r', encoding='utf-8') as f:
            for raw in f:
                line = raw.strip()
                if not line or line.startswith('#'):
                    continue
                # Determine delimiter
                if '=' in line:
                    key, val = line.split('=', 1)
                elif '- ' in line:
                    key, val = line.split('- ', 1)
                elif '-' in line:
                    key, val = line.split('-', 1)
                else:
                    continue
                key = key.strip()
                val = val.strip().strip('"').strip("'")
                if key == 'OPENAI_API_KEY' and val and not os.getenv('OPENAI_API_KEY'):
                    os.environ['OPENAI_API_KEY'] = val
                    break
    except Exception:
        # Fail silent: leave resolution to normal env mechanisms
        pass

# API Configuration
_load_api_key_from_dotenv()
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# Model Options
DEEP_RESEARCH_MODELS = {
    'flagship': 'o3-deep-research-2025-06-26',  # Highest quality, slower
    'fast': 'o4-mini-deep-research-2025-06-26'  # Faster, still high quality
}

# File Paths
PROJECT_ROOT = Path(__file__).parent
PROMPTS_DIR = PROJECT_ROOT / "Prompts"
PDFS_DIR = PROJECT_ROOT / "pdfs"
OUTPUT_DIR = PROJECT_ROOT / "output"

# Input Files
PROMPT_FILE = PROMPTS_DIR / "b_b.txt"
COURSE_OUTLINE_FILE = PDFS_DIR / "Biological and Biochemical.pdf"

# Output Files
DEFAULT_OUTPUT_FILE = OUTPUT_DIR / "generated_questions.json"
BACKUP_OUTPUT_FILE = OUTPUT_DIR / "generated_questions_backup.json"

# Question Generation Settings
QUESTION_SETTINGS = {
    'total_passages': 6,
    'questions_per_passage': 3,
    'discrete_questions': 7,
    'total_questions': 25,
    'difficulty': 'Hard',
    'subject': 'Biological and Biochemical Foundations'
}

# Content Distribution (from original prompt)
CONTENT_DISTRIBUTION = {
    'disciplines': {
        'Biochemistry': 0.40,
        'Molecular_Cell_Bio': 0.35,
        'General_Bio': 0.15,
        'Organic_Chem': 0.10
    },
    'foundational_concepts': {
        'FC1': 0.55,  # biomolecules, processes, structure/function
        'FC2': 0.20,  # cells, organelles, signaling, bioenergetics
        'FC3': 0.25   # organ systems integration, homeostasis, genetics, reproduction
    },
    'reasoning_skills': {
        'Skill1': 0.35,  # concept recall/relationships
        'Skill2': 0.45,  # reasoning/problem solving
        'Skill3': 0.10,  # research design/ethics
        'Skill4': 0.10   # data/statistics
    }
}

# Research Settings
RESEARCH_SETTINGS = {
    'max_search_queries': 20,
    'include_recent_research': True,
    'verify_experimental_parameters': True,
    'citation_required': True,
    'background_mode': True  # For long-running research tasks
}

# Output Format Settings
OUTPUT_FORMAT = {
    'include_metadata': True,
    'include_citations': True,
    'include_explanations': True,
    'include_tags': True,
    'json_indent': 2
}

def ensure_directories():
    """Ensure all required directories exist."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    
def validate_files():
    """Validate that required input files exist."""
    missing_files = []
    
    if not PROMPT_FILE.exists():
        missing_files.append(str(PROMPT_FILE))
    
    if not COURSE_OUTLINE_FILE.exists():
        missing_files.append(str(COURSE_OUTLINE_FILE))
    
    if missing_files:
        raise FileNotFoundError(f"Missing required files: {missing_files}")
    
    return True

def get_model_choice(preference='fast'):
    """Get the appropriate model based on preference."""
    return DEEP_RESEARCH_MODELS.get(preference, DEEP_RESEARCH_MODELS['fast'])

if __name__ == "__main__":
    print("Configuration validation:")
    try:
        ensure_directories()
        validate_files()
        print("✓ All directories and files are ready")
        print(f"✓ Prompt file: {PROMPT_FILE}")
        print(f"✓ Course outline: {COURSE_OUTLINE_FILE}")
        print(f"✓ Output directory: {OUTPUT_DIR}")
        
        if OPENAI_API_KEY:
            print("✓ OpenAI API key is set")
        else:
            print("✗ OpenAI API key is not set")
            
    except Exception as e:
        print(f"✗ Configuration error: {e}")
