#!/usr/bin/env python3
"""
Setup and run script for MCAT Question Generator
This script handles setup, validation, and execution of the question generation process.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
import config
from mcat_question_generator import MCATQuestionGenerator

def setup_environment():
    """Set up the environment and validate requirements."""
    print("=== MCAT Question Generator Setup ===\n")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("✗ Python 3.7 or higher is required")
        return False
    print(f"✓ Python {sys.version.split()[0]}")
    
    # Check required packages
    required_packages = ['openai', 'pdfplumber']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} package available")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} package missing")
    
    if missing_packages:
        print(f"\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # Validate configuration
    try:
        config.ensure_directories()
        config.validate_files()
        print("✓ All required files and directories are ready")
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False
    
    # Check API key
    if not config.OPENAI_API_KEY:
        print("✗ OpenAI API key not found")
        print("Please set your API key:")
        print("  Windows: set OPENAI_API_KEY=your_api_key_here")
        print("  Linux/Mac: export OPENAI_API_KEY=your_api_key_here")
        return False
    print("✓ OpenAI API key is configured")
    
    return True

def choose_model():
    """Let user choose between models."""
    print("\n=== Model Selection ===")
    print("1. o4-mini-deep-research (Recommended: Faster, cost-effective)")
    print("2. o3-deep-research (Highest quality, slower, more expensive)")
    
    while True:
        choice = input("\nSelect model (1 or 2): ").strip()
        if choice == "1":
            return config.DEEP_RESEARCH_MODELS['fast']
        elif choice == "2":
            return config.DEEP_RESEARCH_MODELS['flagship']
        else:
            print("Please enter 1 or 2")

def run_generation(model):
    """Run the question generation process."""
    print(f"\n=== Starting Question Generation ===")
    print(f"Model: {model}")
    print("This process may take 5-30 minutes...")
    print("The system will use deep research to ensure accuracy and current scientific understanding.\n")
    
    try:
        # Initialize generator
        generator = MCATQuestionGenerator()
        
        # Generate questions
        start_time = datetime.now()
        results = generator.generate_questions(model=model)
        end_time = datetime.now()
        
        duration = end_time - start_time
        print(f"\nGeneration completed in {duration}")
        
        if results["success"]:
            print("✓ Questions generated successfully!")
            
            # Create timestamped output file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = config.OUTPUT_DIR / f"mcat_questions_{timestamp}.json"
            
            # Save results
            generator.save_results(results, str(output_file))
            
            # Print summary
            if results["data"]:
                data = results["data"]
                print(f"\n=== Generation Summary ===")
                print(f"Passages: {len(data.get('passages', []))}")
                print(f"Discrete questions: {len(data.get('discrete_questions', []))}")
                print(f"Total questions: {data.get('exam_metadata', {}).get('total_questions', 'Unknown')}")
                print(f"Output file: {output_file}")
                
                # Show citations if available
                if results.get("citations"):
                    print(f"Research sources used: {len(results['citations'])}")
                    
            else:
                print(f"\n=== Raw Output Generated ===")
                print("Content generated but not in expected JSON format.")
                print(f"Check the raw_response in: {output_file}")
                
            return True
            
        else:
            print(f"✗ Generation failed: {results['error']}")
            return False
            
    except Exception as e:
        print(f"✗ Error during generation: {e}")
        return False

def main():
    """Main execution function."""
    
    # Setup and validation
    if not setup_environment():
        print("\n❌ Setup failed. Please resolve the issues above and try again.")
        return
    
    print("\n✅ Setup completed successfully!")
    
    # Model selection
    model = choose_model()
    
    # Confirm before starting
    print(f"\n=== Ready to Generate ===")
    print(f"Model: {model}")
    print(f"Subject: {config.QUESTION_SETTINGS['subject']}")
    print(f"Questions: {config.QUESTION_SETTINGS['total_questions']} total")
    print(f"Format: {config.QUESTION_SETTINGS['total_passages']} passages + {config.QUESTION_SETTINGS['discrete_questions']} discrete")
    
    confirm = input("\nProceed with generation? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Generation cancelled.")
        return
    
    # Run generation
    success = run_generation(model)
    
    if success:
        print("\n🎉 MCAT question generation completed successfully!")
        print("\nNext steps:")
        print("1. Review the generated questions in the output file")
        print("2. Validate the content against your requirements")
        print("3. Use the questions for your MCAT preparation")
    else:
        print("\n❌ Generation failed. Check the error messages above.")

if __name__ == "__main__":
    main()
