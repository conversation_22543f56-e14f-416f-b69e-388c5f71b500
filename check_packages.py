#!/usr/bin/env python3
"""
Check available packages for PDF processing and OpenAI API
"""

def check_package(package_name):
    try:
        __import__(package_name)
        print(f"✓ {package_name} is available")
        return True
    except ImportError:
        print(f"✗ {package_name} is not available")
        return False

def main():
    print("Checking available packages...")
    
    packages = [
        'openai',
        'PyPDF2',
        'pypdf',
        'pdfplumber',
        'fitz',  # PyMuPDF
        'json',
        'os',
        'sys',
        'pathlib'
    ]
    
    available = []
    missing = []
    
    for package in packages:
        if check_package(package):
            available.append(package)
        else:
            missing.append(package)
    
    print(f"\nAvailable packages: {available}")
    print(f"Missing packages: {missing}")
    
    if missing:
        print("\nTo install missing packages, run:")
        for package in missing:
            if package in ['PyPDF2', 'pypdf', 'pdfplumber']:
                print(f"pip install {package}")
            elif package == 'fitz':
                print("pip install PyMuPDF")
            elif package == 'openai':
                print("pip install openai")

if __name__ == "__main__":
    main()
