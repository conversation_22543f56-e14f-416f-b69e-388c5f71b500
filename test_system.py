#!/usr/bin/env python3
"""
Test script for MCAT Question Generator
This script tests the system components without making API calls.
"""

import json
from pathlib import Path
from mcat_question_generator import MCATQuestionGenerator
import config

def test_prompt_loading():
    """Test loading the prompt template."""
    print("Testing prompt loading...")
    try:
        with open(config.PROMPT_FILE, 'r', encoding='utf-8') as f:
            prompt = f.read().strip()
        
        print(f"✓ Prompt loaded successfully ({len(prompt)} characters)")
        print(f"  First 100 characters: {prompt[:100]}...")
        return True
    except Exception as e:
        print(f"✗ Error loading prompt: {e}")
        return False

def test_pdf_extraction():
    """Test PDF content extraction."""
    print("\nTesting PDF extraction...")
    try:
        # Create a temporary generator instance (without API key for testing)
        class TestGenerator:
            def extract_pdf_content(self, pdf_path):
                import pdfplumber
                with pdfplumber.open(pdf_path) as pdf:
                    text_content = []
                    for i, page in enumerate(pdf.pages[:3]):  # Only first 3 pages for test
                        text = page.extract_text()
                        if text:
                            text_content.append(text)
                        if i >= 2:  # Limit for testing
                            break
                    return "\n\n".join(text_content)
        
        test_gen = TestGenerator()
        content = test_gen.extract_pdf_content(config.COURSE_OUTLINE_FILE)
        
        print(f"✓ PDF extracted successfully ({len(content)} characters)")
        print(f"  First 200 characters: {content[:200]}...")
        return True
    except Exception as e:
        print(f"✗ Error extracting PDF: {e}")
        return False

def test_prompt_adaptation():
    """Test prompt adaptation for API."""
    print("\nTesting prompt adaptation...")
    try:
        # Load original prompt
        with open(config.PROMPT_FILE, 'r', encoding='utf-8') as f:
            original_prompt = f.read().strip()
        
        # Create test generator
        class TestGenerator:
            def adapt_prompt_for_api(self, original_prompt, course_content):
                # Simplified version of the adaptation
                adapted = f"""
ROLE: Senior MCAT Item Writer (Biological & Biochemical Foundations) - Deep Research Specialist

ORIGINAL PROMPT ELEMENTS:
{original_prompt[:500]}...

COURSE CONTENT SAMPLE:
{course_content[:500]}...

OUTPUT FORMAT: Return structured JSON with passages and discrete questions.
"""
                return adapted
        
        test_gen = TestGenerator()
        sample_content = "Sample course content for testing..."
        adapted = test_gen.adapt_prompt_for_api(original_prompt, sample_content)
        
        print(f"✓ Prompt adapted successfully ({len(adapted)} characters)")
        print(f"  Adaptation includes original elements and course content")
        return True
    except Exception as e:
        print(f"✗ Error adapting prompt: {e}")
        return False

def create_sample_output():
    """Create a sample output file to demonstrate the expected format."""
    print("\nCreating sample output...")
    try:
        sample_data = {
            "exam_metadata": {
                "title": "MCAT B/B Practice Questions - Deep Research Generated (SAMPLE)",
                "generation_date": "2025-01-05",
                "model_used": "o4-mini-deep-research-2025-06-26",
                "total_questions": 25,
                "note": "This is a sample output format. Actual questions would be generated by the API."
            },
            "passages": [
                {
                    "id": "P1",
                    "title": "CRISPR-mediated knockouts in glycolytic enzymes",
                    "content": "Recent advances in CRISPR-Cas9 technology have enabled precise knockout studies of metabolic enzymes. Researchers investigated the effects of hexokinase knockout on cellular ATP production in HeLa cells. The study utilized a dual-reporter system to monitor both glucose uptake and ATP levels in real-time. Results showed a 75% reduction in ATP production within 6 hours of hexokinase knockout, with compensatory upregulation of alternative metabolic pathways observed after 24 hours.",
                    "research_sources": ["Nature Methods 2024", "Cell Metabolism 2024"],
                    "questions": [
                        {
                            "id": "Q1",
                            "stem": "Based on the experimental design, which of the following would be the most appropriate control for this study?",
                            "choices": {
                                "A": "Cells transfected with non-targeting guide RNA",
                                "B": "Cells treated with glucose-free medium",
                                "C": "Cells overexpressing hexokinase",
                                "D": "Untreated HeLa cells"
                            },
                            "correct_answer": "A",
                            "explanation": "A non-targeting guide RNA control accounts for potential off-target effects of the CRISPR system while maintaining identical experimental conditions except for the specific gene knockout.",
                            "tags": ["FC1", "Biochemistry", "S3"],
                            "difficulty": "Hard"
                        },
                        {
                            "id": "Q2",
                            "stem": "The 75% reduction in ATP production observed after hexokinase knockout is most likely due to:",
                            "choices": {
                                "A": "Decreased glucose transport into cells",
                                "B": "Impaired glycolytic flux at the first committed step",
                                "C": "Reduced mitochondrial oxidative phosphorylation",
                                "D": "Increased glucose export from cells"
                            },
                            "correct_answer": "B",
                            "explanation": "Hexokinase catalyzes the first step of glycolysis, converting glucose to glucose-6-phosphate. Its knockout would severely impair glycolytic flux, reducing ATP production from this pathway.",
                            "tags": ["FC1", "Biochemistry", "S2"],
                            "difficulty": "Hard"
                        },
                        {
                            "id": "Q3",
                            "stem": "The compensatory upregulation of alternative metabolic pathways after 24 hours most likely involves:",
                            "choices": {
                                "A": "Increased fatty acid synthesis",
                                "B": "Enhanced pentose phosphate pathway activity",
                                "C": "Upregulation of fatty acid oxidation and amino acid catabolism",
                                "D": "Increased glycogen synthesis"
                            },
                            "correct_answer": "C",
                            "explanation": "When glycolysis is impaired, cells typically compensate by increasing alternative ATP-generating pathways such as fatty acid oxidation and amino acid catabolism to maintain energy homeostasis.",
                            "tags": ["FC1", "Biochemistry", "S2"],
                            "difficulty": "Hard"
                        }
                    ]
                }
            ],
            "discrete_questions": [
                {
                    "id": "D1",
                    "stem": "Which of the following amino acids would be most likely to disrupt an α-helix when substituted for alanine in the middle of the helix?",
                    "choices": {
                        "A": "Leucine",
                        "B": "Proline",
                        "C": "Serine",
                        "D": "Glutamate"
                    },
                    "correct_answer": "B",
                    "explanation": "Proline is known as a 'helix breaker' because its cyclic side chain restricts backbone flexibility and introduces a kink in the α-helix structure.",
                    "tags": ["FC1", "Biochemistry", "S1"],
                    "difficulty": "Hard"
                }
            ],
            "answer_key": {
                "passages": {
                    "P1Q1": "A",
                    "P1Q2": "B", 
                    "P1Q3": "C"
                },
                "discretes": {
                    "D1": "B"
                }
            }
        }
        
        # Save sample output
        sample_file = config.OUTPUT_DIR / "sample_output.json"
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ Sample output created: {sample_file}")
        print(f"  This shows the expected format for generated questions")
        return True
    except Exception as e:
        print(f"✗ Error creating sample output: {e}")
        return False

def main():
    """Run all tests."""
    print("=== MCAT Question Generator System Test ===\n")
    
    tests = [
        test_prompt_loading,
        test_pdf_extraction,
        test_prompt_adaptation,
        create_sample_output
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✅ All tests passed! The system is ready for use.")
        print("\nNext steps:")
        print("1. Set your OpenAI API key: set OPENAI_API_KEY=your_key_here")
        print("2. Run: python setup_and_run.py")
        print("3. Choose your preferred model and generate questions")
    else:
        print("❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
