# MCAT Question Generator - Deep Research Automation

This project automates the generation of AAMC-style MCAT questions using OpenAI's Deep Research API, replicating the successful approach you used with Gemini deep search.

## 🎯 Project Overview

**Goal**: Automate MCAT B/B question generation using OpenAI's deep research capabilities to overcome web search limitations and generate questions multiple times automatically.

**Key Features**:
- Uses OpenAI's `o3-deep-research` and `o4-mini-deep-research` models
- Processes your existing successful prompt from `Prompts/b_b.txt`
- Integrates the MCAT course outline from `pdfs/Biological and Biochemical.pdf`
- Generates structured JSON output with 25 questions (6 passages + 7 discretes)
- Includes research citations and explanations
- Follows AAMC style guidelines and content distribution

## 📁 Project Structure

```
mcat question generation api/
├── Prompts/
│   └── b_b.txt                    # Your successful prompt
├── pdfs/
│   └── Biological and Biochemical.pdf  # Course outline
├── client approved example/
│   └── BB_MCAT_GEMINI.pdf         # Reference example
├── output/                        # Generated questions (created automatically)
├── mcat_question_generator.py     # Main generator class
├── config.py                      # Configuration settings
├── setup_and_run.py              # Easy-to-use runner script
└── README.md                      # This file
```

## 🚀 Quick Start

### 1. Set Your OpenAI API Key

**Windows:**
```cmd
set OPENAI_API_KEY=your_api_key_here
```

**Linux/Mac:**
```bash
export OPENAI_API_KEY=your_api_key_here
```

### 2. Run the Generator

```bash
python setup_and_run.py
```

This script will:
- Validate your environment and files
- Let you choose between models
- Generate questions using deep research
- Save results to timestamped JSON files

### 3. Alternative: Direct Usage

```python
from mcat_question_generator import MCATQuestionGenerator

# Initialize with your API key
generator = MCATQuestionGenerator(api_key="your_api_key")

# Generate questions (takes 5-30 minutes)
results = generator.generate_questions(model="o4-mini-deep-research-2025-06-26")

# Save results
generator.save_results(results, "my_questions.json")
```

## 🤖 Available Models

1. **o4-mini-deep-research-2025-06-26** (Recommended)
   - Faster execution (5-15 minutes)
   - Cost-effective
   - High quality output
   - Good for regular use

2. **o3-deep-research-2025-06-26** (Premium)
   - Highest quality synthesis
   - Slower execution (15-30 minutes)
   - More expensive
   - Best for final/critical generations

## 📊 Output Format

The system generates a structured JSON file containing:

```json
{
  "exam_metadata": {
    "title": "MCAT B/B Practice Questions - Deep Research Generated",
    "generation_date": "2025-01-05",
    "model_used": "o4-mini-deep-research",
    "total_questions": 25
  },
  "passages": [
    {
      "id": "P1",
      "title": "CRISPR-mediated knockouts in glycolytic enzymes",
      "content": "Research vignette with experimental data...",
      "research_sources": ["source1", "source2"],
      "questions": [
        {
          "id": "Q1",
          "stem": "Question text...",
          "choices": {"A": "...", "B": "...", "C": "...", "D": "..."},
          "correct_answer": "A",
          "explanation": "Detailed reasoning...",
          "tags": ["FC1", "Biochemistry", "S2"],
          "difficulty": "Hard"
        }
      ]
    }
  ],
  "discrete_questions": [...],
  "answer_key": {...}
}
```

## 🎯 Content Distribution

The system follows AAMC guidelines:

**Disciplines:**
- Biochemistry: 40%
- Molecular/Cell Biology: 35%
- General Biology: 15%
- Organic Chemistry: 10%

**Foundational Concepts:**
- FC1 (biomolecules, processes): ~55%
- FC2 (cells, organelles, signaling): ~20%
- FC3 (organ systems, genetics): ~25%

**Scientific Reasoning Skills:**
- Skill 1 (concept recall): ~35%
- Skill 2 (problem solving): ~45%
- Skill 3 (research design): ~10%
- Skill 4 (data analysis): ~10%

## 🔬 Deep Research Features

The system uses OpenAI's deep research to:
- Verify current scientific understanding
- Find recent research developments
- Validate experimental parameters
- Ensure realistic research scenarios
- Include proper citations
- Match current MCAT difficulty standards

## 📝 Customization

### Modify Question Count
Edit `config.py`:
```python
QUESTION_SETTINGS = {
    'total_passages': 6,        # Change number of passages
    'questions_per_passage': 3, # Questions per passage
    'discrete_questions': 7,    # Standalone questions
    'difficulty': 'Hard'        # Easy, Medium, Hard
}
```

### Use Different Course Outlines
Replace or add PDF files in the `pdfs/` directory and update the path in `config.py`.

### Modify Prompts
Edit `Prompts/b_b.txt` to adjust the generation instructions.

## 🛠 Troubleshooting

### Common Issues

1. **API Key Error**
   ```
   Error: OPENAI_API_KEY environment variable not set
   ```
   Solution: Set your OpenAI API key as shown in Quick Start.

2. **File Not Found**
   ```
   FileNotFoundError: Missing required files
   ```
   Solution: Ensure `Prompts/b_b.txt` and `pdfs/Biological and Biochemical.pdf` exist.

3. **Generation Timeout**
   - Deep research can take 5-30 minutes
   - Use `o4-mini-deep-research` for faster results
   - Check your internet connection

4. **JSON Parse Error**
   - The system will save raw output if JSON parsing fails
   - Check the `raw_response` field in the output file
   - The content is still usable, just not in structured format

### Validation

Run the configuration validator:
```bash
python config.py
```

## 💡 Tips for Best Results

1. **Model Selection**: Start with `o4-mini-deep-research` for testing, use `o3-deep-research` for final versions
2. **Timing**: Run during off-peak hours for potentially faster processing
3. **Review**: Always review generated questions for accuracy and appropriateness
4. **Iteration**: Generate multiple sets and combine the best questions
5. **Backup**: The system automatically creates timestamped files to prevent overwrites

## 📈 Comparison with Gemini Deep Search

| Feature | Gemini Deep Search | OpenAI Deep Research |
|---------|-------------------|---------------------|
| Automation | Manual web interface | Fully automated API |
| Repeatability | Limited by web UI | Unlimited via API |
| Customization | Limited | Highly customizable |
| Output Format | PDF | Structured JSON |
| Citations | Included | Included with metadata |
| Speed | Variable | 5-30 minutes |
| Cost | Free (with limits) | Pay-per-use |

## 🔄 Next Steps

1. **Test Generation**: Run a test generation with `o4-mini-deep-research`
2. **Review Output**: Compare with your client-approved example
3. **Iterate**: Adjust prompts or settings as needed
4. **Scale**: Generate multiple question sets for your MCAT prep program
5. **Integrate**: Incorporate into your existing workflow

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Validate your configuration with `python config.py`
3. Review the generated log files in the output directory
4. Ensure your OpenAI API key has sufficient credits and access to deep research models

---

**Ready to generate MCAT questions automatically? Run `python setup_and_run.py` to get started!**
