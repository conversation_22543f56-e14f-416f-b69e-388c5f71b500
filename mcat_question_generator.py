#!/usr/bin/env python3
"""
MCAT Question Generation System using OpenAI Deep Research API
Automates the generation of AAMC-style MCAT questions using deep research capabilities.
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Optional
import pdfplumber
from openai import OpenAI
import config as _cfg

class MCATQuestionGenerator:
    def __init__(self, api_key: str = None):
        """Initialize the MCAT Question Generator with OpenAI API key."""
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass it directly.")
        
        self.client = OpenAI(api_key=self.api_key)
        self.prompt_file = Path("Prompts/b_b.txt")
        self.course_outline_file = Path("pdfs/Biological and Biochemical.pdf")
        
    def load_prompt_template(self) -> str:
        """Load the successful prompt template from b_b.txt."""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"Prompt file not found: {self.prompt_file}")
    
    def extract_pdf_content(self, pdf_path: Path) -> str:
        """Extract text content from PDF file."""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                text_content = []
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
                return "\n\n".join(text_content)
        except Exception as e:
            raise Exception(f"Error extracting PDF content: {e}")
    
    def adapt_prompt_for_api(self, original_prompt: str, course_content: str) -> str:
        """Adapt the original Gemini prompt for OpenAI's Deep Research API."""
        
        # Extract key elements from original prompt
        api_adapted_prompt = f"""
ROLE: Senior MCAT Item Writer (Biological & Biochemical Foundations) - Deep Research Specialist

MISSION: Generate high-quality MCAT B/B questions using comprehensive web research to ensure current scientific accuracy, proper difficulty calibration, and authentic AAMC style. Use deep research capabilities to:
1. Verify current scientific understanding of topics
2. Find recent research developments that could inform question scenarios
3. Validate question difficulty against current MCAT standards
4. Ensure experimental scenarios reflect real-world research practices

CONTENT SCOPE & DISTRIBUTION (B/B ONLY):
- Disciplines: Biochemistry 40%, Molecular/Cell Bio 35%, General Bio 15%, Organic Chem 10%
- Foundational Concepts: FC1 ~55% (biomolecules, processes, structure/function), FC2 ~20% (cells, organelles, signaling, bioenergetics), FC3 ~25% (organ systems integration, homeostasis, genetics, reproduction)
- Scientific Inquiry & Reasoning Skills: Skill1 ~35%, Skill2 ~45%, Skill3 ~10%, Skill4 ~10%
- Difficulty: Hard (matching official MCAT standards)

COURSE OUTLINE CONTEXT:
{course_content[:3000]}  # Truncate to avoid token limits

RESEARCH REQUIREMENTS:
- Search for current research methodologies in biochemistry and molecular biology
- Verify experimental techniques and their applications
- Find recent discoveries that could inform question scenarios
- Research current clinical applications of biochemical principles
- Validate parameter values and experimental conditions

OUTPUT FORMAT: Return a structured JSON object with the following format:
{{
    "exam_metadata": {{
        "title": "MCAT B/B Practice Questions - Deep Research Generated",
        "generation_date": "current_date",
        "total_questions": 25
    }},
    "passages": [
        {{
            "id": "P1",
            "title": "Passage Title with Topic Tag",
            "content": "Passage text (150-350 words) with described data/figures",
            "research_sources": ["source1", "source2"],
            "questions": [
                {{
                    "id": "Q1",
                    "stem": "Question stem",
                    "choices": {{
                        "A": "Choice A",
                        "B": "Choice B", 
                        "C": "Choice C",
                        "D": "Choice D"
                    }},
                    "correct_answer": "A",
                    "explanation": "Detailed explanation with reasoning",
                    "tags": ["FC1", "Biochemistry", "S2"],
                    "difficulty": "Hard"
                }}
            ]
        }}
    ],
    "discrete_questions": [
        {{
            "id": "D1",
            "stem": "Question stem",
            "choices": {{
                "A": "Choice A",
                "B": "Choice B",
                "C": "Choice C", 
                "D": "Choice D"
            }},
            "correct_answer": "A",
            "explanation": "Detailed explanation",
            "tags": ["FC2", "Molecular Biology", "S1"],
            "difficulty": "Hard"
        }}
    ],
    "answer_key": {{
        "passages": {{"P1Q1": "A", "P1Q2": "B", "P1Q3": "C"}},
        "discretes": {{"D1": "A", "D2": "B"}}
    }}
}}

QUALITY STANDARDS:
- Use deep research to ensure all scientific content is current and accurate
- Verify experimental parameters and methodologies through recent literature
- Ensure question scenarios reflect real research being conducted
- Validate difficulty through comparison with official MCAT materials
- Include inline citations for research sources used
- Generate exactly 6 passages (3 questions each) + 7 discrete questions = 25 total questions

Begin deep research and question generation now.
"""
        return api_adapted_prompt
    
    def generate_questions(self, model: str = "o4-mini-deep-research-2025-06-26") -> Dict:
        """Generate MCAT questions using OpenAI's Deep Research API."""
        
        print("Loading prompt template...")
        original_prompt = self.load_prompt_template()
        
        print("Extracting course outline content...")
        course_content = self.extract_pdf_content(self.course_outline_file)
        
        print("Adapting prompt for Deep Research API...")
        adapted_prompt = self.adapt_prompt_for_api(original_prompt, course_content)
        
        print(f"Generating questions using {model}...")
        print("This may take 5-30 minutes due to deep research requirements...")
        
        try:
            response = self.client.responses.create(
                model=model,
                input=[
                    {
                        "role": "developer",
                        "content": [
                            {
                                "type": "input_text",
                                "text": adapted_prompt,
                            }
                        ]
                    }
                ],
                reasoning={
                    "summary": "auto"
                },
                tools=[
                    {
                        "type": "web_search_preview"
                    },
                    {
                        "type": "code_interpreter",
                        "container": {
                            "type": "auto",
                            "file_ids": []
                        }
                    }
                ]
            )
            
            # Extract the final response
            final_response = response.output[-1].content[0].text
            
            # Try to parse as JSON, fallback to text if needed
            try:
                questions_data = json.loads(final_response)
                return {
                    "success": True,
                    "data": questions_data,
                    "raw_response": final_response,
                    "citations": getattr(response.output[-1].content[0], 'annotations', [])
                }
            except json.JSONDecodeError:
                return {
                    "success": True,
                    "data": None,
                    "raw_response": final_response,
                    "citations": getattr(response.output[-1].content[0], 'annotations', []),
                    "note": "Response was not in JSON format, check raw_response"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def save_results(self, results: Dict, output_file: str = "generated_questions.json") -> None:
        """Save the generated questions to a JSON file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"Results saved to {output_file}")

def main():
    """Main function to run the MCAT question generator."""
    
    # Check for API key
    # Importing config ensures .env is parsed and the key is loaded
    api_key = _cfg.OPENAI_API_KEY or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("Error: OPENAI_API_KEY environment variable not set.")
        print("Please set your OpenAI API key:")
        print("  Windows: set OPENAI_API_KEY=your_api_key_here")
        print("  Linux/Mac: export OPENAI_API_KEY=your_api_key_here")
        return
    
    try:
        # Initialize generator
        generator = MCATQuestionGenerator(api_key)
        
        # Generate questions
        print("Starting MCAT question generation...")
        results = generator.generate_questions()
        
        if results["success"]:
            print("✓ Questions generated successfully!")
            
            # Save results
            generator.save_results(results)
            
            # Print summary
            if results["data"]:
                data = results["data"]
                print(f"\nGenerated:")
                print(f"- {len(data.get('passages', []))} passages")
                print(f"- {len(data.get('discrete_questions', []))} discrete questions")
                print(f"- Total: {data.get('exam_metadata', {}).get('total_questions', 'Unknown')} questions")
            else:
                print("\nGenerated content (check raw_response in output file)")
                
        else:
            print(f"✗ Error generating questions: {results['error']}")
            
    except Exception as e:
        print(f"✗ Error: {e}")

if __name__ == "__main__":
    main()
